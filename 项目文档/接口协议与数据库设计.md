# **接口协议与数据库设计**

版本： 1.0 (最终版)  
日期： 2025年7月29日  
本文档是《产品架构设计文档：柴管家》的配套详细设计，旨在明确核心接口协议与数据库表结构，并已预先设计了全面的电商数据模型，作为开发团队实施具体编码工作的蓝图。

## **1\. 接口与协议设计**

### **1.1 消息队列协议 (RabbitMQ)**

#### **1.1.1 上行事件 (Incoming Event) 协议**

**用途：** 将所有外部平台的事件（包括消息、订单更新等）标准化后传递给后端。

**数据结构 (JSON Schema):**

{  
  "event\_id": "unique\_event\_id\_string",  
  "event\_type": "message.incoming" | "order.created" | "order.status\_updated",  
  "channel\_instance\_id": "ci\_shop\_xyz",  
  "timestamp": "2025-07-29T10:00:00Z",  
  "payload": { }   
}

*注：payload结构根据event\_type变化*

**payload.message.content 结构示例:**

* **当 type \= text 时:** "content": "你好"  
* **当 type \= image 时:** "content": { "url": "http://...", "media\_id": "uuid\_for\_asset" }  
* **当 type \= product\_card 时:** "content": { "product\_id": "uuid\_for\_product" }

#### **1.1.2 下行指令 (Outgoing Command) 协议**

**用途：** 指令连接器向外部平台执行操作，如发送消息。

**数据结构 (JSON Schema):**

{  
  "command\_id": "unique\_command\_id\_string",  
  "command\_type": "message.send",  
  "channel\_instance\_id": "ci\_wx\_1a2b3c4d",  
  "timestamp": "2025-07-29T10:05:00Z",  
  "payload": {  
    "conversation\_id": "conv\_wx\_user\_xyz",  
    "recipient\_id": "user\_xyz",  
    "message": {  
      "type": "text",  
      "content": "您好，我们的课程是888元。"  
    }  
  }  
}

### **1.2 后端API协议 (FastAPI)**

#### **1.2.1 REST API (部分核心)**

**前缀：** /api/v1

| 端点 (Endpoint) | 方法 (Method) | 描述 |
| :---- | :---- | :---- |
| /auth/login | POST | 用户登录 |
| /channels | GET, POST | 管理渠道实例 |
| /channels/{channel\_id} | DELETE | 删除一个已接入的渠道实例 |
| /conversations | GET | 获取会话列表 |
| /conversations/{conv\_id}/messages | GET, POST | 获取/发送消息 |
| /kb/faqs | GET, POST | 管理知识库问答对 |
| /products | GET, POST | 管理商品 |
| /orders | GET | 获取订单列表 |
| /customers/{customer\_id} | GET | 获取单个客户的详细画像 |
| /teams | GET, POST | 管理团队 |
| /teams/{team\_id}/members | POST, DELETE | 增删团队成员 |

#### **1.2.2 WebSocket API**

**用途：** 实现前后端实时通信，主动向前端推送更新。

**服务端 \-\> 客户端 事件:**

* **new\_message**: 当收到新消息时推送。  
* **ai\_suggestion**: 当AI生成新的回复建议时推送。  
* **order\_update**: 当订单状态更新时推送。  
* **session\_status\_update**: 当某个会话的状态变更时（如AI接管）。  
* **channel\_status\_update**: 当某个渠道实例的连接状态变更时（如掉线）。

## **2\. 数据库表结构设计 (PostgreSQL)**

**设计原则：** 字段命名采用下划线法。为未来SaaS化，关键业务表预留tenant\_id（租户ID）字段，初期私有化部署可默认为固定值。

#### **users**

| 列名 | 数据类型 | 约束 | 描述 |
| :---- | :---- | :---- | :---- |
| id | UUID | PRIMARY KEY | 用户唯一ID |
| email | VARCHAR(255) | UNIQUE, NOT NULL | 登录邮箱 |
| hashed\_password | VARCHAR(255) | NOT NULL | 加盐哈希后的密码 |
| full\_name | VARCHAR(100) |  | 用户姓名 |
| created\_at | TIMESTAMPTZ | NOT NULL | 创建时间 |
| updated\_at | TIMESTAMPTZ | NOT NULL | 更新时间 |

#### **teams**

存储团队/组织信息。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| id | UUID | PRIMARY KEY | 团队唯一ID |  
| name | VARCHAR(100) | NOT NULL | 团队名称 |  
| owner\_id | UUID | FK(users.id) | 团队所有者 |  
| created\_at | TIMESTAMPTZ | NOT NULL | 创建时间 |

#### **team\_members**

用户与团队的多对多关系表。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| team\_id | UUID | FK(teams.id) | 团队ID |  
| user\_id | UUID | FK(users.id) | 用户ID |  
| role | VARCHAR(50) | NOT NULL | 角色 (e.g., 'admin', 'member') |  
| joined\_at | TIMESTAMPTZ | NOT NULL | 加入时间 |

#### **channel\_instances**

存储用户接入的每一个渠道账号实例。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| id | VARCHAR(50) | PRIMARY KEY | 渠道实例唯一ID (如 ci\_wx\_1a2b3c4d) |  
| team\_id | UUID | FK(teams.id) | 所属团队ID |  
| platform | VARCHAR(50) | NOT NULL | 平台类型 (e.g., 'wechat', 'douyin') |  
| alias | VARCHAR(100) | NOT NULL | 用户设置的别名 (如 "微信咨询A号") |  
| credentials | JSONB | | 加密后的登录凭证 (Token/Cookie等) |  
| status | VARCHAR(50) | NOT NULL | 连接状态 (e.g., 'connected', 'disconnected') |  
| created\_at | TIMESTAMPTZ | NOT NULL | 创建时间 |  
| updated\_at | TIMESTAMPTZ | NOT NULL | 更新时间 |

#### **conversations**

存储每一个对话会话（与单个用户或一个群聊）。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| id | VARCHAR(100) | PRIMARY KEY | 平台侧的会话/群聊ID |  
| channel\_instance\_id | VARCHAR(50) | FK(channel\_instances.id) | 所属渠道实例ID |  
| name | VARCHAR(255) | | 对话名称 (用户昵称或群名) |  
| avatar\_url | TEXT | | 对话头像URL |  
| mode | VARCHAR(50) | NOT NULL | 会话模式 ('manual', 'ai\_managed') |  
| last\_message\_at | TIMESTAMPTZ | | 最新消息时间，用于排序 |  
| created\_at | TIMESTAMPTZ | NOT NULL | 创建时间 |  
| updated\_at | TIMESTAMPTZ | NOT NULL | 更新时间 |

#### **messages**

存储所有消息记录。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| id | BIGSERIAL | PRIMARY KEY | 自增主键ID |  
| platform\_message\_id| VARCHAR(255) | UNIQUE | 消息在平台侧的唯一ID |  
| conversation\_id | VARCHAR(100) | FK(conversations.id) | 所属会话ID |  
| sender\_id | VARCHAR(255) | | 发送者ID |  
| sender\_name | VARCHAR(255) | | 发送者昵称 |  
| direction | VARCHAR(20) | NOT NULL | 消息方向 ('inbound', 'outbound') |  
| type | VARCHAR(50) | NOT NULL | 消息类型 ('text', 'image', etc.) |  
| content | TEXT | NOT NULL | 消息内容 |  
| sent\_at | TIMESTAMPTZ | NOT NULL | 发送时间 |  
| created\_at | TIMESTAMPTZ | NOT NULL | 记录创建时间 |

#### **media\_assets**

管理所有用户上传的媒体文件。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| id | UUID | PRIMARY KEY | 媒体资源唯一ID |  
| user\_id | UUID | FK(users.id) | 上传者ID |  
| file\_name | VARCHAR(255) | NOT NULL | 原始文件名 |  
| file\_type | VARCHAR(50) | | 文件MIME类型 |  
| file\_size | BIGINT | | 文件大小 (Bytes) |  
| storage\_path | TEXT | NOT NULL | 存储路径 (e.g., S3 key or local path) |  
| access\_url | TEXT | | 可访问的URL |  
| created\_at | TIMESTAMPTZ | NOT NULL | 上传时间 |

#### **interaction\_events**

结构化地记录所有关键的客户交互事件，用于构建精准的用户画像和意图分析。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| id | BIGSERIAL | PRIMARY KEY | 事件ID |  
| customer\_id | UUID | FK(customers.id) | 关联的客户ID |  
| channel\_instance\_id| VARCHAR(50) | FK(channel\_instances.id) | 事件发生的渠道 |  
| event\_type | VARCHAR(100) | NOT NULL | 事件类型 (e.g., 'intent\_detected', 'faq\_hit', 'product\_viewed') |  
| metadata | JSONB | | 事件元数据 (e.g., {"intent": "price\_inquiry", "confidence": 0.95}) |  
| created\_at | TIMESTAMPTZ | NOT NULL | 事件发生时间 |

#### **knowledge\_base\_faqs**

存储知识库的问答对。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| id | SERIAL | PRIMARY KEY | 自增主键ID |  
| user\_id | UUID | FK(users.id) | 所属用户ID |  
| question | TEXT | NOT NULL | 标准问题 |  
| similar\_questions | TEXT\[\] | | 相似问法数组 |  
| answer | TEXT | NOT NULL | 标准答案 |  
| created\_at | TIMESTAMPTZ | NOT NULL | 创建时间 |  
| updated\_at | TIMESTAMPTZ | NOT NULL | 更新时间 |

#### **products (商品SPU表)**

存储商品的通用信息 (SPU \- Standard Product Unit)。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| id | UUID | PRIMARY KEY | 商品唯一ID |  
| team\_id | UUID | FK(teams.id) | 所属团队ID |  
| platform\_product\_id | VARCHAR(255) | | 商品在平台的ID (SPU ID) |  
| channel\_instance\_id| VARCHAR(50) | FK(channel\_instances.id) | 所属渠道实例 |  
| title | VARCHAR(255) | NOT NULL | 商品标题 |  
| description | TEXT | | 商品描述 (富文本) |  
| main\_image\_url | TEXT | | 商品主图URL |  
| category | VARCHAR(100) | | 商品分类 |  
| tags | TEXT\[\] | | 商品标签 |  
| is\_active | BOOLEAN | DEFAULT true | 是否上架 |  
| created\_at | TIMESTAMPTZ | NOT NULL | 创建时间 |  
| updated\_at | TIMESTAMPTZ | NOT NULL | 更新时间 |

#### **product\_variants (商品规格SKU表)**

存储商品的具体规格、价格和库存 (SKU \- Stock Keeping Unit)。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| id | UUID | PRIMARY KEY | SKU唯一ID |  
| product\_id | UUID | FK(products.id) | 所属商品ID |  
| platform\_sku\_id | VARCHAR(255) | NOT NULL, UNIQUE | SKU在平台的ID |  
| attributes | JSONB | | 规格属性 (如 {"颜色": "红色", "尺码": "S"}) |  
| price | DECIMAL(10, 2\) | NOT NULL | 价格 |  
| cost\_price | DECIMAL(10, 2\) | | 成本价 |  
| stock\_quantity | INTEGER | DEFAULT 0 | 库存数量 |  
| image\_url | TEXT | | SKU对应图片 |  
| created\_at | TIMESTAMPTZ | NOT NULL | 创建时间 |  
| updated\_at | TIMESTAMPTZ | NOT NULL | 更新时间 |

#### **customers**

存储客户档案信息。  
| 列名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| id | UUID | PRIMARY KEY | 客户唯一ID |
| team\_id | UUID | FK(teams.id) | 所属团队ID |
| platform\_customer\_id | VARCHAR(255) | NOT NULL, UNIQUE| 客户在平台的ID |
| name | VARCHAR(255) | | 客户姓名 |
| phone | VARCHAR(50) | | 电话 |
| email | VARCHAR(255) | | 邮箱 |
| tags | TEXT\[\] | | 客户标签 |
| total\_spent | DECIMAL(12, 2\) | DEFAULT 0 | 累计消费金额 |
| first\_seen\_at | TIMESTAMPTZ | NOT NULL | 首次出现时间 |
| last\_seen\_at | TIMESTAMPTZ | NOT NULL | 最近出现时间 |

#### **orders (订单主表)**

存储订单的总体信息。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| id | UUID | PRIMARY KEY | 订单唯一ID |  
| platform\_order\_id | VARCHAR(255) | NOT NULL, UNIQUE | 订单在平台的ID |  
| channel\_instance\_id| VARCHAR(50) | FK(channel\_instances.id) | 所属渠道实例 |  
| customer\_id | UUID | FK(customers.id) | 关联的客户ID |  
| status | VARCHAR(50) | NOT NULL | 订单状态 (pending, paid, shipped, cancelled) |  
| total\_amount | DECIMAL(10, 2\) | NOT NULL | 订单总金额 |  
| shipping\_fee | DECIMAL(10, 2\) | | 运费 |  
| payment\_method | VARCHAR(50) | | 支付方式 |  
| shipping\_address | JSONB | | 收货地址 |  
| notes | TEXT | | 订单备注 |  
| order\_created\_at | TIMESTAMPTZ | NOT NULL | 订单在平台的创建时间 |  
| updated\_at | TIMESTAMPTZ | NOT NULL | 记录更新时间 |

#### **order\_items (订单项表)**

存储订单中包含的具体商品项。  
| 列名 | 数据类型 | 约束 | 描述 |  
| :--- | :--- | :--- | :--- |  
| id | UUID | PRIMARY KEY | 订单项唯一ID |  
| order\_id | UUID | FK(orders.id) | 所属订单ID |  
| product\_variant\_id | UUID | FK(product\_variants.id) | 关联的SKU ID |  
| product\_title | VARCHAR(255) | NOT NULL | 商品标题 (冗余，方便查询) |  
| variant\_attributes | JSONB | | 规格属性 (冗余，方便查询) |  
| quantity | INTEGER | NOT NULL | 购买数量 |  
| unit\_price | DECIMAL(10, 2\) | NOT NULL | 购买时的单价 |