---
type: "always_apply"
---

\# 开发工作流 V1.0

本文档为“柴管家”项目的AI开发者定义了一套必须严格遵守的工作流程和行为规则。其目标是确保所有开发活动都在GitHub上规范、透明、自动化地进行，并与我们制定的《开发规划文档》保持高度一致。

\---

\#\# 一、 核心原则

1\.  \*\*单一信息源原则\*\*: GitHub是项目管理的唯一真实信息源。所有任务的状态以GitHub Projects为准，所有任务的规格以GitHub Issues为准。  
2\.  \*\*BDD驱动原则\*\*: 严格遵循“编写失败测试 \-\> 编写产品代码 \-\> 通过测试”的BDD三步走开发循环。绝不允许在没有对应失败测试的情况下编写任何产品代码。  
3\.  \*\*主干开发原则\*\*: 所有开发工作都在功能分支上进行，\`main\`主干分支始终保持可部署状态。绝不允许直接向\`main\`分支提交代码。  
4\.  \*\*自动化一切原则\*\*: 所有可自动化的检查（代码风格、测试、构建）都必须通过CI/CD流水线来强制执行。

\---

\#\# 二、 标准开发工作流（The Workflow）

在处理任何一个开发任务时，都必须遵循以下七个步骤的完整循环。

\#\#\# \*\*步骤 1: 领取任务与创建分支\*\*

1\.  \*\*领取任务\*\*: 从GitHub项目板的\`待办 (To Do)\`列中，选取优先级最高（最顶部的）一个Issue作为当前任务。Issue的标题应使用\*\*中文\*\*描述。  
2\.  \*\*更新状态\*\*: 立即将该Issue在项目板上从\`待办 (To Do)\`列拖拽到\`进行中 (In Progress)\`列。  
3\.  \*\*创建分支\*\*:  
    \* 从最新的\`main\`分支创建一条新的功能分支。  
    \* \*\*分支命名必须规范\*\*: \`feature/issue\<ISSUE\_NUMBER\>-\<short-description\>\`。  
        \* \*\*命名规则\*\*: 分支名中的\`\<short-description\>\`部分，必须使用对应Issue中文标题的\*\*英文简写或拼音\*\*，并用短横线连接，以确保URL和命令行友好。  
        \* \*\*示例\*\*: 如果领取的任务是Issue \#1，其标题为“用户可以接入闲鱼账号”，则分支名应为 \`feature/issue1-connect-xianyu-account\`。

\#\#\# \*\*步骤 2: BDD三步走实现\*\*

这是开发的核心循环，针对Issue描述中的每一个验收标准（Acceptance Criteria）重复此过程。

1\.  \*\*第一步：编写失败的测试\*\*  
    \* 读取验收标准（例如，“系统必须提供一个‘添加新账号’的入口”）。  
    \* 在代码库的测试目录中，编写一个\*\*刚好能复现该功能缺失\*\*的自动化测试（如前端的组件测试或后端的API测试）。  
    \* 运行测试，并\*\*确认它因为功能未实现而失败\*\*。将这个失败的测试文件暂存（\`git add\`）。

2\.  \*\*第二步：编写产品代码\*\*  
    \* 编写\*\*最精简的、刚好能让上一步的失败测试通过\*\*的产品代码。

3\.  \*\*第三步：通过测试\*\*  
    \* 再次运行测试，\*\*确认之前失败的测试现在已经通过\*\*，并且没有破坏任何其他现有测试。  
    \* 将相关的产品代码和测试代码暂存（\`git add\`）。

\#\#\# \*\*步骤 3: 提交代码 (Commit)\*\*

1\.  当一个完整的用户故事（即一个Issue中的所有验收标准）都通过BDD循环开发完毕后，进行代码提交。  
2\.  \*\*Commit信息必须规范\*\*: 使用约定式提交（Conventional Commits）格式。  
    \* \*\*格式\*\*: \`\<type\>(\<scope\>): \<subject\>\`  
    \* \*\*规则\*\*: \`\<type\>\`和\`\<scope\>\`使用英文，但\`\<subject\>\`部分必须使用\*\*中文\*\*，清晰地描述本次提交的内容。  
    \* \*\*示例\*\*:  
        \* 新功能: \`feat(api): 新增用于创建渠道账号的端点\`  
        \* 修复Bug: \`fix(ui): 修正登录页面的按钮对齐问题\`  
        \* 测试相关: \`test(api): 为创建账号功能添加集成测试\`  
    \* 在Commit的正文中，必须使用关键词关联对应的Issue，例如 \`Closes \#1\`。

\#\#\# \*\*步骤 4: 发起拉取请求 (Pull Request \- PR)\*\*

1\.  将本地的功能分支推送到远程仓库。  
2\.  立即创建一个从该功能分支到\`main\`分支的拉取请求（PR）。  
3\.  \*\*PR描述必须规范\*\*:  
    \* \*\*标题\*\*: 必须使用\*\*中文\*\*，且与对应的Issue标题保持一致，方便审查。  
    \* \*\*正文\*\*:  
        \* 必须使用\`Closes \#\<ISSUE\_NUMBER\>\`关键词自动关联对应的Issue。  
        \* 必须包含一个简短的中文实现说明。  
        \* 必须包含一个\*\*测试清单\*\*，说明该PR通过了哪些手动或自动测试，引用我们的“完成的定义(DoD)”。

\#\#\# \*\*步骤 5: 自动化检查 (CI/CD)\*\*

1\.  PR创建后，CI/CD流水线将自动触发。  
2\.  流水线将执行所有自动化检查，包括：代码风格检查（Linting）、单元测试、集成测试、构建过程。  
3\.  \*\*所有检查必须全部通过\*\*，PR才具备被审查的资格。如果任何检查失败，AI开发者必须修复问题并提交新的Commit，直至流水线通过。

\#\#\# \*\*步骤 6: 人工审查与合并\*\*

1\.  当PR通过所有自动化检查后，它会自动被分配给项目负责人进行人工代码审查（Code Review）。  
2\.  项目负责人审查代码的逻辑、架构和安全性。  
3\.  只有在获得项目负责人的\*\*明确批准（Approve）\*\*后，该PR才能被合并到\`main\`分支。

\#\#\# \*\*步骤 7: 完成任务\*\*

1\.  当PR被成功合并到\`main\`分支后，GitHub会自动关闭与之关联的Issue。  
2\.  自动化规则会将已关闭的Issue在项目板上从\`进行中 (In Progress)\`列移动到\`已完成 (Done)\`列。  
3\.  至此，一个开发任务的完整生命周期结束。AI开发者可以返回步骤1，领取下一个任务。

\---

\#\# 三、 工作流可视化摘要

\`\`\`mermaid  
graph TD  
    subgraph "GitHub项目板"  
        A\[待办 To Do\] \--\> B\[进行中 In Progress\] \--\> C\[已完成 Done\]  
    end

    subgraph "开发流程"  
        D(1. 领取Issue \#1\\n"用户可以接入闲鱼账号") \--\> E{2. 创建分支\\nfeature/issue1-connect-xianyu-account};  
        E \--\> F\[3. BDD循环开发\];  
        F \--\> G{4. 提交代码\\nfeat(api): 新增渠道账号端点\\nCloses \#1};  
        G \--\> H\[5. 创建PR\\n"用户可以接入闲鱼账号"\];  
        H \--\> I{6. CI/CD自动检查};  
        I \-- 通过 \--\> J\[7. 人工审查\];  
        J \-- 批准 \--\> K\[8. 合并到main\];  
    end

    A \-- AI拖动 \--\> B;  
      
    K \-- 自动关闭Issue \#1 \--\> C;  
