---
type: "always_apply"
---

# **模块化单体架构设计规范**

版本: 1.0  
日期: 2025年7月25日

## **1\. 核心原则与理念**

本规范旨在为基于领域驱动设计（DDD）的模块化单体应用提供一套标准化的架构、结构与命名约定。其最终目标是构建一个**高内聚、低耦合、易于维护和演进**的系统。

### **1.1. 架构选型**

我们选择**模块化单体**架构，以平衡微服务的结构优势与单体应用的运营简便性。此架构的成功依赖于严格的纪律和自动化治理。

### **1.2. 模块划分原则**

* **唯一标准**：系统的模块划分**必须**严格遵循**领域驱动设计（DDD）的限界上下文（Bounded Context）**。每一个模块即一个限界上下文。  
* **垂直切分**：模块必须按**业务能力进行垂直切分**，封装从API端点到底层数据访问的全部逻辑。严禁采用传统的按技术分层（如 UI, Business, Data）进行模块划分。

### **1.3. 数据所有权原则**

* **数据隔离**：每个模块对其领域数据拥有**绝对的所有权**。任何其他模块都**禁止**直接访问不属于自己的数据表。  
* **通信方式**：跨模块的数据访问**必须**通过目标模块定义的**公共API**（同步命令/查询或异步事件）进行。

## **2\. 项目与代码库结构**

### **2.1. 代码库策略**

* 采用\*\*单一代码库（Monorepo）\*\*策略，以简化依赖管理、保证版本一致性并方便大规模重构。

### **2.2. 高层级目录结构**

推荐使用以下顶层结构，以清晰地分离关注点：

/YourSolution.Root/  
│  
├── /docs/  
│   └── MODULES.md              \# 描述所有模块、负责人及其核心职责的文档  
│  
├── /src/  
│   ├── /Host/                  \# 1\. 主机应用：承载所有模块的入口点 (如 ASP.NET Core Web API)  
│   │   └── YourSolution.Host.csproj  
│   │  
│   ├── /Modules/               \# 2\. 业务模块：所有限界上下文的根目录  
│   │   ├── /Ordering/          \#    \- 示例模块1: 订单上下文  
│   │   └── /Catalog/           \#    \- 示例模块2: 商品目录上下文  
│   │  
│   └── /BuildingBlocks/        \# 3\. 构建块：跨模块共享的可复用基础设施组件  
│       ├── /Shared.Kernel/     \#    \- 真正通用的核心类型、接口和抽象 (无外部依赖)  
│       └── /Shared.Infrastructure/ \#    \- 共享的基础设施实现 (如事件总线、认证等)  
│  
├── /tests/  
│   ├── /Architecture.Tests/      \# 4\. 架构测试：用于强制执行架构规则  
│   ├── /Modules.Tests/  
│   │   ├── /Ordering.Tests/  
│   │   └── /Catalog.Tests/  
│  
└── YourSolution.sln

### **2.3. 模块内部结构**

每个模块本身都是一个独立的、遵循\*\*整洁架构（Clean Architecture）**与**垂直切片架构（Vertical Slice Architecture）\*\*思想的微型应用。

以 Ordering 模块为例：

/Ordering/  
│  
├── /Api/                 \# 1\. 公共契约 (DTOs) \- 模块的公共接口  
│   ├── /Commands/  
│   ├── /Queries/  
│   └── /Events/  
│  
├── /Application/         \# 2\. 应用逻辑 \- 按功能/用例进行垂直切片  
│   ├── /PlaceOrder/  
│   │   ├── PlaceOrderCommand.cs  
│   │   └── PlaceOrderCommandHandler.cs  
│   └── /GetOrderDetails/  
│       ├── GetOrderDetailsQuery.cs  
│       └── GetOrderDetailsQueryHandler.cs  
│  
├── /Domain/              \# 3\. 领域模型 \- 模块的核心业务逻辑  
│   ├── /Aggregates/  
│   ├── /Entities/  
│   ├── /ValueObjects/  
│   └── /Events/          \#    \- 领域事件  
│  
├── /Infrastructure/      \# 4\. 基础设施 \- 外部依赖的实现  
│   ├── /Persistence/  
│   │   ├── Repositories/  
│   │   └── OrderingDbContext.cs  
│   └── /Services/  
│  
├── /Endpoints/           \# 5\. API端点 \- 模块的入口 (如 Minimal APIs, Controllers)  
│  
└── OrderingModule.cs     \# 6\. 模块注册 \- 定义模块的依赖注入和服务

## **3\. 命名规范**

一致的命名是实现代码清晰性和架构可发现性的关键。

| 元素 | 规范 | 示例 |
| :---- | :---- | :---- |
| **解决方案** | CompanyName.ProjectName | MyCorp.Ecommerce |
| **模块项目** | CompanyName.ProjectName.Modules.\<BoundedContext\> | MyCorp.Ecommerce.Modules.Ordering |
| **命令** | 祈使动词 \+ 名词 \+ Command | PlaceOrderCommand |
| **查询** | Get \+ 名词 \+ Query | GetOrderDetailsQuery |
| **命令/查询处理器** | 对应类名 \+ Handler | PlaceOrderCommandHandler |
| **领域事件** | 名词 \+ 过去式动词 \+ Event | OrderPlacedEvent |
| **集成事件** | 同领域事件，用于跨模块通信 | OrderPlacedIntegrationEvent |
| **仓储接口** | I \+ 聚合名 \+ Repository | IOrderRepository |
| **文件名** | 与内部主要公共类名完全匹配 | PlaceOrderCommand.cs |

## **4\. 数据隔离与管理策略**

我们采用**共享数据库，权限隔离**的策略来强制执行模块的数据所有权。

### **4.1. 数据库结构**

* **Schema隔离**: 每个模块**必须**在共享的物理数据库中拥有自己独立的Schema。  
  * Ordering 模块的所有表都位于 ordering Schema下 (例如 ordering.orders, ordering.order\_items)。  
  * Catalog 模块的所有表都位于 catalog Schema下 (例如 catalog.products, catalog.categories)。

### **4.2. 数据库用户与权限**

* **用户隔离**: 每个模块**必须**使用独立的数据库用户/角色进行数据访问。  
  * Ordering 模块的应用代码连接数据库时，使用 ordering\_user。  
  * Catalog 模块的应用代码连接数据库时，使用 catalog\_user。  
* **权限限制**: **必须**在数据库层面配置严格的权限。  
  * ordering\_user **仅**拥有对 ordering Schema的 SELECT, INSERT, UPDATE, DELETE 权限。  
  * ordering\_user **绝对不能**拥有对 catalog 或任何其他Schema的任何访问权限。

### **4.3. 规则与禁令**

* **禁止跨Schema查询**: 由于数据库权限的限制，任何形式的跨模块数据库 JOIN 或直接查询都将被物理阻止。这是架构的**硬性边界**。  
* **数据访问**: 当 Ordering 模块需要 Catalog 模块的数据时（例如，获取产品名称），它**必须**通过调用 Catalog 模块的公共API（例如，发送一个 GetProductDetailsQuery）来实现。

## **5\. 模块间通信**

| 通信类型 | 推荐模式 | 用途 | 耦合度 |
| :---- | :---- | :---- | :---- |
| **同步（请求/响应）** | **中介者模式 (MediatR)** | 当一个模块需要立即从另一个模块获取数据或触发一个必须立即完成的操作时。 | 中等 |
| **异步（事件驱动）** | **内存事件总线** | 当一个模块的状态变更需要通知其他模块，但不需要立即得到响应时。这是解耦的首选。 | **低** |

## **6\. 架构治理与强制执行**

人的纪律是不可靠的。架构的完整性**必须**通过自动化测试来保障。

* **工具**: 使用 **NetArchTest** (对于.NET) 或 **ArchUnit** (对于Java) 在 Architecture.Tests 项目中创建架构测试。  
* **核心测试用例**:  
  1. **模块边界测试**: 断言 Ordering 模块的项目不能直接引用 Catalog 模块的项目。唯一允许的引用是 BuildingBlocks 和公共契约（如果单独发布）。  
  2. **整洁架构依赖测试**: 断言 Domain 层不能依赖 Application 或 Infrastructure 层。  
  3. **命名约定测试**: 断言所有位于 Application 层且名称以 Command 结尾的类，都必须有一个对应的 CommandHandler。  
  4. **公共API可见性测试**: 断言 Domain 层的内部类型（如实体）不能通过 Api 层的公共方法签名暴露出去。

**规范结束**